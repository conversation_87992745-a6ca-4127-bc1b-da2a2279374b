/** * Appointment Slice using shared CRUD patterns * Refactored to use the generic createCrudSlice factory */ import {
    createCrudSlice
  } from '../../shared/store/createCrudSlice';
  import appointmentService, {
    type Appointment
  } from '../../services/appointmentService';
// Create the appointment slice using the generic factory

const appointmentCrud = createCrudSlice<Appointment>('appointment', appointmentService, {
  // Additional custom reducers specific to appointments
  setAppointmentStatus: (state, action) => {
    const {
    id, status
  } = action.payload;
  const appointment = state.items.find(item => item.id === id);
  if (appointment) {
    appointment.status = status;
  }
  if (state.selectedItem?.id === id) {
    state.selectedItem.status = status;
  }
  }, filterByStatus: (state, action) => {
    state.filters = { ...state.filters, status: action.payload
  };
  }, filterByDoctor: (state, action) => {
    state.filters = { ...state.filters, doctor: action.payload
  };
  }, filterByDate: (state, action) => {
    state.filters = { ...state.filters, date: action.payload
  };
  },
  } ); // Export the slice components export

const {
    slice, actions, reducer, thunks
  } = appointmentCrud; // Export specific actions for easier use export

const {
    setSelectedItem: setSelectedAppointment, setFilters, updateFilters, clearFilters, setSearchQuery, clearError, reset, optimisticAdd, optimisticUpdate, optimisticRemove, // Custom actions setAppointmentStatus, filterByStatus, filterByDoctor, filterByDate,
  } = actions; // Export thunks for async operations export

const {
    fetchAll: fetchAppointments, fetchById: fetchAppointment, create: createAppointment, update: updateAppointment, delete: deleteAppointment, bulkDelete: bulkDeleteAppointments,
  } = thunks; // Export the reducer as default export default reducer;
// Legacy exports for backward compatibility export

const setAppointments = (appointments: Appointment[]) => actions.optimisticAdd(appointments[0]);
// This is a simplified mapping export

const setLoading = (loading: boolean) => loading ? {
    type: 'appointment/fetchAll/pending'
  } : {
    type: 'appointment/fetchAll/fulfilled', payload: {
    data: []
  }
  };
  export

const setError = (error: string | null) => ({
    type: 'appointment/fetchAll/rejected', payload: error
  });
