import {
    createSlice, createAsyncThunk
  } from '@reduxjs/toolkit';
  import type {
    PayloadAction
  } from '@reduxjs/toolkit';
  import type {
    User, LoginCredentials, RegisterData
  } from '../../types/auth';
  import {
    authAPI
  } from '../../utils/api';
  interface AuthState {
    user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  }

const initialState: AuthState = {
    user: null, token: localStorage.getItem('token'), refreshToken: localStorage.getItem('refreshToken'), isAuthenticated: !!localStorage.getItem('token'), isLoading: false, error: null,
  }; // Async thunks export

const login = createAsyncThunk( 'auth/login', async (credentials: LoginCredentials, {
    rejectWithValue
  }) => {
    try {
    const response = await authAPI.login(credentials);
  localStorage.setItem('token', response.tokens.access);
  localStorage.setItem('refreshToken', response.tokens.refresh);
  return response;
  } catch (error: any) {
    return rejectWithValue(error.response?.data?.error || 'Login failed');
  }
  } );
  export

const register = createAsyncThunk( 'auth/register', async (userData: RegisterData, {
    rejectWithValue
  }) => {
    try {
    const response = await authAPI.register(userData);
  localStorage.setItem('token', response.tokens.access);
  localStorage.setItem('refreshToken', response.tokens.refresh);
  return response;
  } catch (error: any) { // Handle validation errors from Django
  if (error.response?.data) {
    const errorData = error.response.data;
  if (typeof errorData === 'object' && !errorData.error) { // Format validation errors for display

const errorMessages = Object.entries(errorData).map(([field, errors]: [string, any]) => {
    const fieldErrors = Array.isArray(errors) ? errors : [errors];
  return `${
    field
  }: ${
    fieldErrors.map((e: any) => e.string || e).join(', ')
  }`;
  }).join('\n');
  return rejectWithValue(errorMessages);
  } return rejectWithValue(errorData.error || 'Registration failed');
  } return rejectWithValue('Registration failed');
  }
  } );
  export

const logout = createAsyncThunk( 'auth/logout', async (_, {
    getState, rejectWithValue
  }) => {
    try {
    const state = getState() as {
    auth: AuthState
  };
  if (state.auth.refreshToken) {
    await authAPI.logout(state.auth.refreshToken);
  } localStorage.removeItem('token');
  localStorage.removeItem('refreshToken');
  return null;
  } catch (error: any) {
    localStorage.removeItem('token');
  localStorage.removeItem('refreshToken');
  return rejectWithValue(error.response?.data?.error || 'Logout failed');
  }
  } );
  export

const fetchProfile = createAsyncThunk( 'auth/fetchProfile', async (_, {
    rejectWithValue
  }) => {
    try {
    const response = await authAPI.getProfile();
  return response;
  } catch (error: any) {
    return rejectWithValue(error.response?.data?.error || 'Failed to fetch profile');
  }
  } );
  const authSlice = createSlice({
    name: 'auth', initialState, reducers: {
    clearError: (state) => {
    state.error = null;
  }, setCredentials: (state, action: PayloadAction<{
    user: User;
  token: string;
  refreshToken: string
  }>) => {
    state.user = action.payload.user;
  state.token = action.payload.token;
  state.refreshToken = action.payload.refreshToken;
  state.isAuthenticated = true;
  },
  },
  extraReducers: (builder) => {
    builder
      // Login
      .addCase(login.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.token = action.payload.tokens.access;
        state.refreshToken = action.payload.tokens.refresh;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
  state.isAuthenticated = false;
  }) // Register .addCase(register.pending, (state) => {
    state.isLoading = true;
  state.error = null;
  }) .addCase(register.fulfilled, (state, action) => {
    state.isLoading = false;
  state.user = action.payload.user;
  state.token = action.payload.tokens.access;
  state.refreshToken = action.payload.tokens.refresh;
  state.isAuthenticated = true;
  state.error = null;
  }) .addCase(register.rejected, (state, action) => {
    state.isLoading = false;
  state.error = action.payload as string;
  state.isAuthenticated = false;
  }) // Logout .addCase(logout.fulfilled, (state) => {
    state.user = null;
  state.token = null;
  state.refreshToken = null;
  state.isAuthenticated = false;
  state.error = null;
  }) // Fetch Profile .addCase(fetchProfile.pending, (state) => {
    state.isLoading = true;
  }) .addCase(fetchProfile.fulfilled, (state, action) => {
    state.isLoading = false;
  state.user = action.payload;
  }) .addCase(fetchProfile.rejected, (state, action) => {
    state.isLoading = false;
  state.error = action.payload as string;
  });
  },
  });
  export

const {
    clearError, setCredentials
  } = authSlice.actions;
  export default authSlice.reducer;
