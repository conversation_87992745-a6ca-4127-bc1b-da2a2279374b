/**
 * Patient Service
 * Handles all patient-related API operations using BaseApiService
 */

import { BaseApiService } from '../shared/services/BaseApiService';
import type { BaseEntity } from '../shared/types/api';

// Patient interface extending BaseEntity
export interface Patient extends BaseEntity {
  patient_id: string;
  user: {
    id: number;
    full_name: string;
    email: string;
    phone_number?: string;
  };
  blood_group?: string;
  allergies?: string;
  chronic_conditions?: string;
  current_medications?: string;
  insurance_provider?: string;
  insurance_policy_number?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
}

// Patient medical history interface
export interface PatientMedicalHistory {
  id: number;
  patient_id: string;
  date: string;
  doctor: string;
  department: string;
  diagnosis: string;
  status: string;
  notes: string;
}

// Patient prescription interface
export interface PatientPrescription {
  id: number;
  medication: string;
  prescribedBy: string;
  date: string;
  duration: string;
  instructions: string;
  status: string;
}

class PatientService extends BaseApiService<Patient> {
  constructor() {
    super({
      baseURL: '/patients',
      endpoints: {
        list: '/patients/',
        detail: '/patients/:id/',
        create: '/patients/',
        update: '/patients/:id/',
        delete: '/patients/:id/',
      },
    });
  }

  // Get current patient data (for logged-in patient)
  async getCurrentPatient(): Promise<Patient> {
    return this.get<Patient>('/patients/current/');
  }

  // Get patient medical history
  async getPatientMedicalHistory(patientId: string | number): Promise<PatientMedicalHistory[]> {
    return this.get<PatientMedicalHistory[]>(`/patients/${patientId}/medical-history/`);
  }

  // Get patient prescriptions
  async getPatientPrescriptions(patientId: string | number): Promise<PatientPrescription[]> {
    return this.get<PatientPrescription[]>(`/patients/${patientId}/prescriptions/`);
  }

  // Get patient appointments
  async getPatientAppointments(patientId: string | number, params?: any) {
    return this.get(`/patients/${patientId}/appointments/`, { params });
  }

  // Update patient medical information
  async updateMedicalInfo(patientId: string | number, medicalInfo: Partial<Patient>) {
    return this.update(patientId, medicalInfo);
  }

  // Search patients by various criteria
  async searchPatients(query: string, filters?: any) {
    return this.getAll({
      search: query,
      ...filters,
    });
  }

  // Get patients by blood group
  async getPatientsByBloodGroup(bloodGroup: string) {
    return this.filter({ blood_group: bloodGroup });
  }

  // Get patients by insurance provider
  async getPatientsByInsurance(insuranceProvider: string) {
    return this.filter({ insurance_provider: insuranceProvider });
  }
}

// Create and export a singleton instance
const patientService = new PatientService();
export default patientService;
