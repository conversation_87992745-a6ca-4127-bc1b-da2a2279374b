/**
 * Doctor Service
 * Handles all doctor-related API operations
 */

import { BaseApiService } from './baseApiService';

export interface Doctor {
  id: number;
  user: {
    id: number;
    full_name: string;
    email: string;
    phone_number?: string;
  };
  specialization: string;
  license_number: string;
  department: {
    id: number;
    name: string;
  };
  experience_years?: number;
  rating?: number;
  bio?: string;
  consultation_fee?: number;
  available_days?: string[];
  available_hours?: {
    start: string;
    end: string;
  };
  created_at: string;
  updated_at: string;
}

export interface DoctorSchedule {
  id: number;
  doctor: number;
  day_of_week: number; // 0-6 (Monday-Sunday)
  start_time: string;
  end_time: string;
  is_available: boolean;
}

export interface DoctorAvailability {
  doctor_id: number;
  date: string;
  available_slots: string[];
  booked_slots: string[];
}

class DoctorService extends BaseApiService {
  constructor() {
    super('/doctors');
  }

  // Get all doctors with optional filtering
  async getDoctors(params?: {
    department?: number;
    specialization?: string;
    available_date?: string;
    search?: string;
    page?: number;
    page_size?: number;
  }) {
    return this.get('/doctors/', { params });
  }

  // Get doctor by ID
  async getDoctorById(id: number) {
    return this.get(`/doctors/${id}/`);
  }

  // Get doctors by department
  async getDoctorsByDepartment(departmentId: number) {
    return this.get('/doctors/', {
      params: { department: departmentId }
    });
  }

  // Get doctor's schedule
  async getDoctorSchedule(doctorId: number) {
    return this.get(`/doctors/${doctorId}/schedule/`);
  }

  // Get doctor's availability for a specific date
  async getDoctorAvailability(doctorId: number, date: string) {
    return this.get(`/doctors/${doctorId}/availability/`, {
      params: { date }
    });
  }

  // Get available time slots for a doctor on a specific date
  async getAvailableSlots(doctorId: number, date: string) {
    return this.get(`/doctors/${doctorId}/available-slots/`, {
      params: { date }
    });
  }

  // Create new doctor (admin only)
  async createDoctor(doctorData: Partial<Doctor>) {
    return this.post('/doctors/', doctorData);
  }

  // Update doctor information
  async updateDoctor(id: number, doctorData: Partial<Doctor>) {
    return this.patch(`/doctors/${id}/`, doctorData);
  }

  // Delete doctor (admin only)
  async deleteDoctor(id: number) {
    return this.delete(`/doctors/${id}/`);
  }

  // Update doctor schedule
  async updateDoctorSchedule(doctorId: number, scheduleData: DoctorSchedule[]) {
    return this.post(`/doctors/${doctorId}/schedule/`, { schedule: scheduleData });
  }

  // Get doctor statistics
  async getDoctorStats(doctorId: number) {
    return this.get(`/doctors/${doctorId}/stats/`);
  }

  // Get doctor's patients
  async getDoctorPatients(doctorId: number, params?: {
    page?: number;
    page_size?: number;
    search?: string;
  }) {
    return this.get(`/doctors/${doctorId}/patients/`, { params });
  }

  // Get doctor's appointments
  async getDoctorAppointments(doctorId: number, params?: {
    date?: string;
    status?: string;
    page?: number;
    page_size?: number;
  }) {
    return this.get(`/doctors/${doctorId}/appointments/`, { params });
  }
}

// Create and export a singleton instance
const doctorService = new DoctorService();
export default doctorService;
