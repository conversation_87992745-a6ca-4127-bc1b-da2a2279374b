/** * Health check utilities for HMS frontend * Refactored to use shared BaseApiService infrastructure */ import {
    BaseApiService
  } from '../shared/services/BaseApiService';
  import aiService from '../services/aiService';
  export interface HealthCheckResult {
    backend: boolean;
  api: boolean;
  auth: boolean;
  ai: boolean;
  timestamp: string;
}

// Create a simple health check service using BaseApiService
class HealthCheckService extends BaseApiService {
  constructor() {
    super({
    baseURL: '', endpoints: {
    list: '/', detail: '/:id/',
  },
  });
  } async checkBackend(): Promise<boolean> {
    try {
    await this.get('/');
  return true;
  } catch (error) {
    console.warn('Backend health check failed:', error);
  return false;
  }
  } async checkAuth(): Promise<boolean> {
    try { // Use OPTIONS request to check auth endpoints without authentication

const response = await this.api.options('/auth/');
  return response.status < 400;
  } catch (error) {
    console.warn('Auth health check failed:', error);
  return false;
  }
  }
  }

const healthService = new HealthCheckService();
  export class HealthChecker {
/** * Check if backend server is accessible */ static async checkBackend(): Promise<boolean> {
    return healthService.checkBackend();
  } /** * Check if authentication endpoints are working */ static async checkAuth(): Promise<boolean> {
    return healthService.checkAuth();
  } /** * Check if AI services are accessible */ static async checkAI(): Promise<boolean> {
    try {
    await aiService.getHealthStatus();
  return true;
  } catch (error) {
    console.warn('AI health check failed:', error);
  return false;
  }
  }

  /**
   * Perform comprehensive health check
   */
  static async performHealthCheck(): Promise<HealthCheckResult> {
    const [backend, auth, ai] = await Promise.allSettled([
      this.checkBackend(),
      this.checkAuth(),
      this.checkAI(),
    ]);

    return {
      backend: backend.status === 'fulfilled' ? backend.value : false,
      api: backend.status === 'fulfilled' ? backend.value : false,
      auth: auth.status === 'fulfilled' ? auth.value : false,
      ai: ai.status === 'fulfilled' ? ai.value : false,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Display health check results in console
   */
  static async logHealthStatus(): Promise<void> {
    const health = await this.performHealthCheck();
  console.group('🏥 HMS Health Check');
  console.log(`🔧 Backend: ${
    health.backend ? '✅ Online' : '❌ Offline'
  }`);
  console.log(`🔐 Auth: ${
    health.auth ? '✅ Working' : '❌ Failed'
  }`);
  console.log(`🤖 AI Services: ${
    health.ai ? '✅ Available' : '❌ Unavailable'
  }`);
  console.log(`⏰ Checked at: ${
    health.timestamp
  }`);
  console.groupEnd();
  if (!health.backend) {
    console.warn('⚠️ Backend server appears to be offline. Please ensure Django server is running on http://127.0.0.1:8000/');
  }
  if (!health.ai) {
    console.warn('⚠️ AI services are not available. Some features may be limited.');
  }
  }
  } // Auto-run health check in development
  if (process.env.NODE_ENV === 'development') { // Run health check after a short delay to allow app to initialize setTimeout(() => {
    HealthChecker.logHealthStatus().catch(console.error);
  }, 2000);
  }
