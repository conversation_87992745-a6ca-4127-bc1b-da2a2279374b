/**
 * Patient Slice using shared CRUD patterns
 * Refactored to use the real patient service instead of mock data
 */

import { createCrudSlice } from '../../shared/store/createCrudSlice';
import patientService, { type Patient } from '../../services/patientService'; // Create the patient slice using the generic factory with real service
const patientCrud = createCrudSlice<Patient>('patient', patientService, {
  // Additional custom reducers specific to patients
  updateMedicalInfo: (state, action) => {
    const { id, medicalInfo } = action.payload;
    const patient = state.items.find(item => item.id === id);
    if (patient) {
      Object.assign(patient, medicalInfo);
    }
    if (state.selectedItem?.id === id) {
      Object.assign(state.selectedItem, medicalInfo);
    }
  },
  filterByBloodGroup: (state, action) => {
    state.filters = { ...state.filters, blood_group: action.payload };
  },
  filterByInsurance: (state, action) => {
    state.filters = { ...state.filters, insurance_provider: action.payload };
  },
}); // Export the slice components
const { slice, actions, reducer, thunks } = patientCrud;

// Export specific actions for easier use
export const {
  setSelectedItem: setSelectedPatient,
  setFilters,
  updateFilters,
  clearFilters,
  setSearchQuery,
  clearError,
  reset,
  optimisticAdd,
  optimisticUpdate,
  optimisticRemove,
  // Custom actions
  updateMedicalInfo,
  filterByBloodGroup,
  filterByInsurance,
} = actions;

// Export thunks for async operations
export const {
  fetchAll: fetchPatients,
  fetchById: fetchPatient,
  create: createPatient,
  update: updatePatient,
  delete: deletePatient,
  bulkDelete: bulkDeletePatients,
} = thunks;

// Export the reducer as default
export default reducer;

// Export the patient service for direct use if needed
export { patientService };
