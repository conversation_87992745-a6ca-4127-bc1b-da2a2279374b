import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/Input';
import { useTheme } from '../../hooks/useTheme';
import { useApi } from '../../shared/hooks/useApi';
import { useCrud } from '../../shared/hooks/useCrud';
import { prescriptionService, type Prescription } from '../../services/medicalService';
import { Pill, Plus, Search, Calendar, User, Clock, Eye, Edit, Download, Filter, AlertCircle, Loader2 } from 'lucide-react';
const PrescriptionManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isDark } = useTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');

  // Use CRUD hook for prescriptions
  const {
    items: prescriptions,
    loading,
    error,
    fetchAll: fetchPrescriptions,
    create: createPrescription,
    update: updatePrescription,
    delete: deletePrescription,
  } = useCrud(prescriptionService, {
    immediate: true,
  });

  // Search functionality with API
  const {
    data: searchResults,
    loading: searchLoading,
    execute: performSearch,
  } = useApi((query: string, filters: any) =>
    prescriptionService.searchPrescriptions(query, filters), {
    immediate: false,
  });

  // Handle search with debouncing
  useEffect(() => {
    if (searchTerm.trim()) {
      const timeoutId = setTimeout(() => {
        performSearch(searchTerm, { status: selectedFilter !== 'all' ? selectedFilter : undefined });
      }, 300);
      return () => clearTimeout(timeoutId);
    }
  }, [searchTerm, selectedFilter, performSearch]);

  // Use search results if available, otherwise use all prescriptions
  const displayPrescriptions = searchTerm.trim() ? (searchResults || []) : prescriptions;

  // Filter prescriptions based on status filter
  const filteredPrescriptions = displayPrescriptions.filter(prescription => {
    const matchesFilter = selectedFilter === 'all' || prescription.status === selectedFilter;
    return matchesFilter;
  });
  const getStatusColor = (status: string) => {
    switch (status) {
    case 'active': return 'status-success ';
  case 'completed': return 'status-info ';
  case 'discontinued': return 'status-error ';
  default: return 'bg-muted text-foreground dark:bg-gray-800 dark:text-gray-300';
  }
  };
  const isExpiringSoon = (endDate: string) => {
    const today = new Date();
  const expiry = new Date(endDate);
  const daysUntilExpiry = Math.ceil((expiry.getTime() - today.getTime()) / (1000 * 3600 * 24));
  return daysUntilExpiry <= 7 && daysUntilExpiry > 0;
  };
  const isExpired = (endDate: string) => {
    const today = new Date();
  const expiry = new Date(endDate);
  return expiry < today;
  };
  return ( <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6"> <div className="max-w-7xl mx-auto space-y-6"> {/* Header */
  }
    <Card className="glass border-0 shadow-xl">
    <CardHeader> <div className="flex items-center justify-between"> <div className="flex items-center gap-4"> <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
    <Pill className="w-6 h-6 text-white" /> </div> <div>
    <CardTitle className="text-2xl font-bold macos-text-primary"> {
    t('prescriptions.title')
  }
    </CardTitle> <p className="macos-text-secondary"> {
    t('prescriptions.subtitle')
  } </p> </div> </div>
    <Button variant="glass" onClick={() => setShowAddForm(true)
  } className="flex items-center gap-2">
    <Plus className="w-4 h-4" /> {
    t('prescriptions.newPrescription')
  }
    </Button> </div>
    </CardHeader>
    </Card> {/* Filters and Search */
  }
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex flex-col md:flex-row gap-4"> <div className="flex-1"> <div className="relative">
    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 macos-text-tertiary w-4 h-4" />
    <Input placeholder={
    t('common.searchPlaceholder')
  } value={
    searchTerm
  } onChange={(e) => setSearchTerm(e.target.value)
  } className="pl-10" variant="glass" /> </div> </div> <div className="flex items-center gap-3">
    <Filter className="w-4 h-4 macos-text-secondary" /> <select value={
    selectedFilter
  } onChange={(e) => setSelectedFilter(e.target.value)
  } className="glass border-0 rounded-xl px-4 py-2 text-sm macos-text-primary focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50" > <option value="all">{
    t('common.all')
  } {
    t('prescriptions.activePrescriptions')
  }</option> <option value="active">{
    t('common.active')
  }</option> <option value="completed">{
    t('common.completed')
  }</option> <option value="discontinued">{
    t('prescriptions.discontinue')
  }</option> </select> </div> </div>
    </CardContent>
    </Card> {/* Statistics */
  } <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">{
    t('common.total')
  } {
    t('prescriptions.activePrescriptions')
  }</p> <p className="text-2xl font-bold macos-text-primary">{
    prescriptions.length
  }</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
    <Pill className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card>
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">{
    t('common.active')
  }</p> <p className="text-2xl font-bold text-emerald-700 dark:text-emerald-400 dark:text-green-400"> {
    prescriptions.filter(p => p.status === 'active').length
  } </p> </div> <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
    <Clock className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card>
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">{
    t('inventory.expiringSoon')
  }</p> <p className="text-2xl font-bold text-orange-600 dark:text-orange-400"> {
    prescriptions.filter(p => isExpiringSoon(p.end_date)).length
  } </p> </div> <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
    <AlertCircle className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card>
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">{
    t('common.thisMonth')
  }</p> <p className="text-2xl font-bold text-purple-600 dark:text-purple-400"> {
    prescriptions.filter(p => p.startDate.startsWith('2024-12')).length
  } </p> </div> <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
    <Calendar className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card> </div> {/* Prescriptions List */
  } <div className="space-y-4"> {
    filteredPrescriptions.map((prescription) => (
    <Card key={
    prescription.id
  } className="glass border-0 shadow-lg hover:glass-hover macos-transition">
    <CardContent className="p-6"> <div className="flex items-start justify-between"> <div className="flex-1"> <div className="flex items-center gap-4 mb-4"> <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
    <Pill className="w-5 h-5 text-white" /> </div> <div className="flex-1"> <h3 className="text-lg font-semibold macos-text-primary"> {
    prescription.medication_name
  } </h3> <div className="flex items-center gap-2 mt-1">
    <Badge className={`${
    getStatusColor(prescription.status)
  } rounded-full px-3 py-1`
  }> {
    prescription.status
  }
    </Badge> {
    isExpiringSoon(prescription.endDate) && (
    <Badge className="bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400 rounded-full px-3 py-1 flex items-center gap-1">
    <AlertCircle className="w-3 h-3" /> {
    t('prescriptions.expiringSoon')
  }
    </Badge> )
  } {
    isExpired(prescription.endDate) && (
    <Badge className="status-error rounded-full px-3 py-1"> {
    t('prescriptions.expired')
  }
    </Badge> )
  } </div> </div> </div> <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4"> <div> <p className="text-sm macos-text-secondary">{
    t('prescriptions.patient')
  }: <span className="macos-text-primary font-medium">{
    prescription.patient?.user?.full_name || 'Unknown Patient'
  }</span></p> <p className="text-sm macos-text-secondary">ID: <span className="macos-text-primary font-medium">{
    prescription.patient?.patient_id || 'N/A'
  }</span></p> <p className="text-sm macos-text-secondary">{
    t('common.doctor')
  }: <span className="macos-text-primary font-medium">{
    prescription.doctor?.full_name || 'Unknown Doctor'
  }</span></p> </div> <div> <p className="text-sm macos-text-secondary">{
    t('prescriptions.dosage')
  }: <span className="macos-text-primary font-medium">{
    prescription.dosage
  }</span></p> <p className="text-sm macos-text-secondary">{
    t('prescriptions.frequency')
  }: <span className="macos-text-primary font-medium">{
    prescription.frequency
  }</span></p> <p className="text-sm macos-text-secondary">{
    t('prescriptions.duration')
  }: <span className="macos-text-primary font-medium">{
    prescription.duration
  }</span></p> </div> <div> <p className="text-sm macos-text-secondary">{
    t('prescriptions.start')
  }: <span className="macos-text-primary font-medium">{
    prescription.start_date
  }</span></p> <p className="text-sm macos-text-secondary">{
    t('prescriptions.end')
  }: <span className="macos-text-primary font-medium">{
    prescription.end_date
  }</span></p> <p className="text-sm macos-text-secondary"> {
    t('prescriptions.refills')
  }: <span className="macos-text-primary font-medium">{
    prescription.refillsRemaining
  }/{
    prescription.totalRefills
  }</span> </p> </div> </div> <div className="glass-subtle border border-purple-200/50 dark:border-purple-800/50 rounded-xl p-4"> <p className="text-sm font-semibold macos-text-primary mb-1">{
    t('prescriptions.instructions')
  }:</p> <p className="text-sm text-purple-800 dark:text-purple-400">{
    prescription.instructions
  }</p> </div> </div> <div className="flex flex-col gap-2 ml-6">
    <Button variant="glass" size="sm" className="flex items-center gap-2">
    <Eye className="w-4 h-4" /> {
    t('common.view')
  }
    </Button>
    <Button variant="glass" size="sm" className="flex items-center gap-2">
    <Edit className="w-4 h-4" /> {
    t('common.edit')
  }
    </Button>
    <Button variant="glass" size="sm" className="flex items-center gap-2">
    <Download className="w-4 h-4" /> {
    t('prescriptions.print')
  }
    </Button> </div> </div>
    </CardContent>
    </Card> ))
  } </div> {
    filteredPrescriptions.length === 0 && (
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-12 text-center"> <div className="w-16 h-16 bg-gradient-to-br from-gray-400 to-gray-500 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
    <Pill className="w-8 h-8 text-white" /> </div> <h3 className="text-lg font-semibold macos-text-primary mb-2">{
    t('prescriptions.noPrescriptionsFound')
  }</h3> <p className="macos-text-secondary"> {
    searchTerm ? t('common.adjustSearchCriteria') : t('prescriptions.startByCreating')
  } </p>
    </CardContent>
    </Card> )
  } </div> </div> );
  };
  export default PrescriptionManagement;
