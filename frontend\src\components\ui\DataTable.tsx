import React, {
    useState
  } from 'react';
  import {
    useTranslation
  } from 'react-i18next';
  import {
    Button
  } from './Button';
  import {
    Input
  } from './Input';
  import {
    useTheme
  } from '../../hooks/useTheme';
  interface Column
    <T> {
    key: keyof T | string;
  title: string;
  render?: (value: any, record: T) => React.ReactNode;
  sortable?: boolean;
  filterable?: boolean;
  width?: string;
  } interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  pagination?: {
    current: number;
  pageSize: number;
  total: number;
  onChange: (page: number, pageSize: number) => void;
  };
  onSearch?: (searchTerm: string) => void;
  onSort?: (key: string, direction: 'asc' | 'desc') => void;
  onFilter?: (filters: Record<string, any>) => void;
  actions?: {
    title: string;
  render: (record: T) => React.ReactNode;
  };
  selectable?: {
    selectedRowKeys: string[];
  onChange: (selectedRowKeys: string[]) => void;
  getRowKey: (record: T) => string;
  };
  }

function DataTable
    <T>({
    data = [], columns, loading = false, pagination, onSearch, onSort, onFilter, actions, selectable,
  }: DataTableProps
    <T>) { // Ensure data is always an array

const safeData = Array.isArray(data) ? data : [];
  const {
    t
  } =
  useTranslation();
  const {
    isDark
  } =
  useTheme();
  const [searchTerm, setSearchTerm] =
  useState('');
  const [sortConfig, setSortConfig] = useState<{
    key: string;
  direction: 'asc' | 'desc';
  } | null>(null);
  const [filters, setFilters] = useState
    <Record<string, any>>({
  });
  const handleSearch = (value: string) => {
    setSearchTerm(value);
  onSearch?.(value);
  };
  const handleSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc';
  if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
    direction = 'desc';
  } setSortConfig({
    key, direction
  });
  onSort?.(key, direction);
  };
  const handleSelectAll = (checked: boolean) => {
    if (!selectable) return;
  if (checked) {
    const allKeys = safeData.map(selectable.getRowKey);
  selectable.onChange(allKeys);
  } else {
    selectable.onChange([]);
  }
  };
  const handleSelectRow = (rowKey: string, checked: boolean) => {
    if (!selectable) return;
  let newSelectedKeys = [...selectable.selectedRowKeys];
  if (checked) {
    newSelectedKeys.push(rowKey);
  } else {
    newSelectedKeys = newSelectedKeys.filter(key => key !== rowKey);
  } selectable.onChange(newSelectedKeys);
  };
  const isAllSelected = selectable && safeData.length > 0 && safeData.every(record => selectable.selectedRowKeys.includes(selectable.getRowKey(record)));
  const isIndeterminate = selectable && selectable.selectedRowKeys.length > 0 && !isAllSelected;
  return ( <div className="glass rounded-xl border-0 shadow-xl overflow-hidden"> {/* Header with search and filters */
  } <div className="p-6 border-b border-white/10 dark:border-white/5"> <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"> <div className="flex-1 max-w-md">
    <Input variant="glass" placeholder={
    t('ui.searchPlaceholder')
  } value={
    searchTerm
  } onChange={(e) => handleSearch(e.target.value)
  } className="w-full" /> </div> {
    selectable && selectable.selectedRowKeys.length > 0 && ( <div className="text-sm macos-text-secondary"> {
    selectable.selectedRowKeys.length
  } {
    t('ui.itemsSelected')
  } </div> )
  } </div> </div> {/* Table */
  } <div className="overflow-x-auto"> <table className="min-w-full divide-y divide-white/10 dark:divide-white/5"> <thead className="glass-subtle"> <tr> {
    selectable && ( <th className="px-6 py-4 text-left text-xs font-medium macos-text-secondary uppercase tracking-wider"> <input type="checkbox" checked={
    isAllSelected
  } ref={(input) => {
    if (input) input.indeterminate = isIndeterminate;
  }
  } onChange={(e) => handleSelectAll(e.target.checked)
  } className="rounded border-white/20 text-sky-700 dark:text-sky-400 focus:ring-blue-500 bg-background/10" /> </th> )
  } {
    columns.map((column, index) => ( <th key={
    index
  } className="px-6 py-4 text-left text-xs font-medium macos-text-secondary uppercase tracking-wider" style={{
    width: column.width
  }
  } > <div className="flex items-center space-x-1"> <span>{
    column.title
  }</span> {
    column.sortable && ( <button onClick={() => handleSort(column.key as string)
  } className="macos-text-tertiary hover:macos-text-primary macos-transition" > <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2
  } d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" /> </svg> </button> )
  } </div> </th> ))
  } {
    actions && ( <th className="px-6 py-4 text-left text-xs font-medium macos-text-secondary uppercase tracking-wider"> {
    actions.title
  } </th> )
  } </tr> </thead> <tbody className="divide-y divide-white/10 dark:divide-white/5"> {
    loading ? ( <tr> <td colSpan={
    columns.length + (selectable ? 1 : 0) + (actions ? 1 : 0)
  } className="px-6 py-8 text-center macos-text-secondary" > <div className="flex items-center justify-center"> <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div> <span className="ml-2">{
    t('common.loading')
  }</span> </div> </td> </tr> ) : safeData.length === 0 ? ( <tr> <td colSpan={
    columns.length + (selectable ? 1 : 0) + (actions ? 1 : 0)
  } className="px-6 py-8 text-center macos-text-secondary" > {
    t('messages.noData')
  } </td> </tr> ) : ( safeData.map((record, rowIndex) => {
    const rowKey = selectable ? selectable.getRowKey(record) : rowIndex.toString();
  const isSelected = selectable ? selectable.selectedRowKeys.includes(rowKey) : false;
  return ( <tr key={
    rowKey
  } className={`hover:glass-hover macos-transition ${
    isSelected ? 'glass-selected' : ''
  }`
  } > {
    selectable && ( <td className="px-6 py-4 whitespace-nowrap"> <input type="checkbox" checked={
    isSelected
  } onChange={(e) => handleSelectRow(rowKey, e.target.checked)
  } className="rounded border-white/20 text-sky-700 dark:text-sky-400 focus:ring-blue-500 bg-background/10" /> </td> )
  } {
    columns.map((column, colIndex) => {
    let value;
  try {
    if (column.key.toString().includes('.')) { // Handle nested properties safely

const keys = column.key.toString().split('.');
  value = keys.reduce((obj, key) => {
    return obj && typeof obj === 'object' && key in obj ? obj[key] : undefined;
  }, record);
  } else {
    value = (record as any)[column.key];
  }
  } catch (error) {
    console.warn(`Error accessing property ${
    column.key
  }:`, error);
  value = undefined;
  }
  return ( <td key={
    colIndex
  } className="px-6 py-4 whitespace-nowrap text-sm macos-text-primary"> {
    column.render ? column.render(value, record) : ( value !== null && value !== undefined ? (typeof value === 'object' ? (value.name || value.title || value.description || value.label || (Array.isArray(value) ? value.join(', ') : JSON.stringify(value))) : String(value) ) : '-' )
  } </td> );
  })
  } {
    actions && ( <td className="px-6 py-4 whitespace-nowrap text-sm macos-text-primary"> {
    actions.render(record)
  } </td> )
  } </tr> );
  }) )
  } </tbody> </table> </div> {/* Pagination */
  } {
    pagination && ( <div className="px-6 py-4 border-t border-white/10 dark:border-white/5 flex items-center justify-between"> <div className="text-sm macos-text-secondary"> {
    t('ui.showingResults')
  } {((pagination.current - 1) * pagination.pageSize) + 1
  } {
    t('ui.to')
  }{' '
  } {
    Math.min(pagination.current * pagination.pageSize, pagination.total)
  } {
    t('ui.of')
  }{' '
  } {
    pagination.total
  } {
    t('ui.results')
  } </div> <div className="flex items-center space-x-2">
    <Button variant="glass" size="sm" onClick={() => pagination.onChange(pagination.current - 1, pagination.pageSize)
  } disabled={
    pagination.current <= 1
  } > {
    t('common.previous')
  }
    </Button> <span className="text-sm macos-text-secondary"> {
    t('ui.page')
  } {
    pagination.current
  } {
    t('ui.pageOf')
  } {
    Math.ceil(pagination.total / pagination.pageSize)
  } </span>
    <Button variant="glass" size="sm" onClick={() => pagination.onChange(pagination.current + 1, pagination.pageSize)
  } disabled={
    pagination.current >= Math.ceil(pagination.total / pagination.pageSize)
  } > {
    t('common.next')
  }
    </Button> </div> </div> )
  } </div> );
  } export default DataTable;
