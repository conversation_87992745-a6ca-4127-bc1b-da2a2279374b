import {
    BaseApiService
  } from '../shared/services/BaseApiService';
  import type {
    BaseEntity
  } from '../shared/types';
  export interface Appointment extends BaseEntity {
    appointment_id: string;
  patient: {
    id: number;
  patient_id: string;
  user: {
    id: number;
  full_name: string;
  email: string;
  };
  };
  doctor: {
    id: number;
  full_name: string;
  email: string;
  };
  appointment_date: string;
  appointment_time: string;
  duration_minutes: number;
  appointment_type: string;
  status: string;
  reason_for_visit: string;
  notes?: string;
  consultation_fee: number;
  } export interface AppointmentCreate {
    patient: number;
  doctor: number;
  appointment_date: string;
  appointment_time: string;
  duration_minutes?: number;
  appointment_type: string;
  reason_for_visit: string;
  notes?: string;
  consultation_fee?: number;
  } export interface AppointmentSlot {
    id: number;
  doctor: {
    id: number;
  full_name: string;
  };
  date: string;
  start_time: string;
  end_time: string;
  is_available: boolean;
  max_appointments: number;
  } class AppointmentService extends BaseApiService
    <Appointment> {
    constructor() {
    super({
    baseURL: '/appointments', endpoints: {
    list: '/appointments/', detail: '/appointments/:id/', create: '/appointments/', update: '/appointments/:id/', delete: '/appointments/:id/',
  },
  });
  }

  // Custom appointment actions using the base service's customAction method
  async confirmAppointment(id: number) {
    return this.customAction(id, 'confirm');
  }

  async cancelAppointment(id: number) {
    return this.customAction(id, 'cancel');
  }

  async completeAppointment(id: number) {
    return this.customAction(id, 'complete');
  }

  async markNoShow(id: number) {
    return this.customAction(id, 'no_show');
  }

  async getTodayAppointments() {
    return this.get('/appointments/today/');
  } async getUpcomingAppointments() {
    return this.get('/appointments/upcoming/');
  } // Appointment Slots - using base service methods async getAppointmentSlots(params?: any) {
    return this.get('/slots/', {
    params
  });
  } async getAvailableSlots(doctorId?: number, date?: string) {
    const params: any = {
  };
  if (doctorId) params.doctor_id = doctorId;
  if (date) params.date = date;
  return this.get('/slots/available/', {
    params
  });
  } async createAppointmentSlot(data: any) {
    return this.post('/slots/', data);
  } async updateAppointmentSlot(id: number, data: any) {
    return this.patch('/slots/:id/', data, {
    params: {
    id
  }
  });
  } async deleteAppointmentSlot(id: number) {
    return this.delete('/slots/:id/', {
    params: {
    id
  }
  });
  } // Recurring Appointments - using base service methods async getRecurringAppointments(params?: any) {
    return this.get('/recurring/', {
    params
  });
  } async createRecurringAppointment(data: any) {
    return this.post('/recurring/', data);
  } async updateRecurringAppointment(id: number, data: any) {
    return this.patch('/recurring/:id/', data, {
    params: {
    id
  }
  });
  } async deleteRecurringAppointment(id: number) {
    return this.delete('/recurring/:id/', {
    params: {
    id
  }
  });
  } async activateRecurringAppointment(id: number) {
    return this.post('/recurring/:id/activate/', {
  }, {
    params: {
    id
  }
  });
  } async deactivateRecurringAppointment(id: number) {
    return this.post('/recurring/:id/deactivate/', {
  }, {
    params: {
    id
  }
  });
  }

  // Additional methods for appointment booking
  // Note: These methods delegate to dedicated services for better separation of concerns
  async getDepartments() {
    // Import here to avoid circular dependencies
    const { default: departmentService } = await import('./departmentService');
    return departmentService.getDepartments();
  }

  async getDoctorsByDepartment(departmentId: string) {
    // Import here to avoid circular dependencies
    const { default: departmentService } = await import('./departmentService');
    return departmentService.getDepartmentDoctors(parseInt(departmentId));
  }

  async getAppointmentTypes() {
    return this.get('/appointment-types/');
  }

  async getAppointmentsByDateRange(startDate: string, endDate: string) {
    return this.get('/appointments/', {
      params: {
        start_date: startDate,
        end_date: endDate
      }
    });
  }
}

export default new AppointmentService();
