/**
 * Department Service
 * Handles all department-related API operations
 */

import { BaseApiService } from './baseApiService';

export interface Department {
  id: number;
  name: string;
  description: string;
  icon?: string;
  head_doctor?: {
    id: number;
    full_name: string;
  };
  location?: string;
  phone_number?: string;
  email?: string;
  operating_hours?: {
    start: string;
    end: string;
  };
  services?: string[];
  doctor_count?: number;
  patient_count?: number;
  created_at: string;
  updated_at: string;
}

export interface DepartmentStats {
  total_doctors: number;
  total_patients: number;
  total_appointments: number;
  appointments_today: number;
  average_rating: number;
  revenue_this_month: number;
}

class DepartmentService extends BaseApiService {
  constructor() {
    super('/departments');
  }

  // Get all departments
  async getDepartments(params?: {
    search?: string;
    page?: number;
    page_size?: number;
  }) {
    return this.get('/departments/', { params });
  }

  // Get department by ID
  async getDepartmentById(id: number) {
    return this.get(`/departments/${id}/`);
  }

  // Get department statistics
  async getDepartmentStats(id: number) {
    return this.get(`/departments/${id}/stats/`);
  }

  // Get doctors in a department
  async getDepartmentDoctors(id: number, params?: {
    page?: number;
    page_size?: number;
    search?: string;
    available_date?: string;
  }) {
    return this.get(`/departments/${id}/doctors/`, { params });
  }

  // Get patients in a department
  async getDepartmentPatients(id: number, params?: {
    page?: number;
    page_size?: number;
    search?: string;
  }) {
    return this.get(`/departments/${id}/patients/`, { params });
  }

  // Get department appointments
  async getDepartmentAppointments(id: number, params?: {
    date?: string;
    status?: string;
    page?: number;
    page_size?: number;
  }) {
    return this.get(`/departments/${id}/appointments/`, { params });
  }

  // Create new department (admin only)
  async createDepartment(departmentData: Partial<Department>) {
    return this.post('/departments/', departmentData);
  }

  // Update department information
  async updateDepartment(id: number, departmentData: Partial<Department>) {
    return this.patch(`/departments/${id}/`, departmentData);
  }

  // Delete department (admin only)
  async deleteDepartment(id: number) {
    return this.delete(`/departments/${id}/`);
  }

  // Get department services/specializations
  async getDepartmentServices(id: number) {
    return this.get(`/departments/${id}/services/`);
  }

  // Update department services
  async updateDepartmentServices(id: number, services: string[]) {
    return this.post(`/departments/${id}/services/`, { services });
  }

  // Get department equipment/inventory
  async getDepartmentEquipment(id: number) {
    return this.get(`/departments/${id}/equipment/`);
  }

  // Get department revenue/financial data
  async getDepartmentRevenue(id: number, params?: {
    start_date?: string;
    end_date?: string;
    period?: 'daily' | 'weekly' | 'monthly';
  }) {
    return this.get(`/departments/${id}/revenue/`, { params });
  }

  // Get all departments with basic info (for dropdowns/selects)
  async getDepartmentsList() {
    return this.get('/departments/list/');
  }

  // Search departments by name or services
  async searchDepartments(query: string) {
    return this.get('/departments/search/', {
      params: { q: query }
    });
  }
}

// Create and export a singleton instance
const departmentService = new DepartmentService();
export default departmentService;
