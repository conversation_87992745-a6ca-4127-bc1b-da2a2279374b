/**
 * Medical Service
 * Handles medical records, prescriptions, and lab tests API operations
 */

import { BaseApiService } from '../shared/services/BaseApiService';
import type { BaseEntity } from '../shared/types/api';

// Medical Record interface
export interface MedicalRecord extends BaseEntity {
  medical_record_id: string;
  patient: {
    id: number;
    patient_id: string;
    user: {
      full_name: string;
    };
  };
  doctor: {
    id: number;
    full_name: string;
  };
  visit_date: string;
  diagnosis: string;
  symptoms: string;
  treatment: string;
  notes: string;
  status: 'active' | 'completed' | 'follow-up';
  priority: 'low' | 'medium' | 'high';
}

// Prescription interface
export interface Prescription extends BaseEntity {
  prescription_id: string;
  patient: {
    id: number;
    patient_id: string;
    user: {
      full_name: string;
    };
  };
  doctor: {
    id: number;
    full_name: string;
  };
  medication_name: string;
  dosage: string;
  frequency: string;
  duration: string;
  quantity: number;
  instructions: string;
  status: 'active' | 'completed' | 'cancelled';
  prescribed_date: string;
  start_date: string;
  end_date: string;
}

// Lab Test interface
export interface LabTest extends BaseEntity {
  test_id: string;
  patient: {
    id: number;
    patient_id: string;
    user: {
      full_name: string;
    };
  };
  doctor: {
    id: number;
    full_name: string;
  };
  test_name: string;
  test_type: 'blood' | 'urine' | 'imaging' | 'biopsy' | 'other';
  status: 'ordered' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'routine' | 'urgent' | 'stat';
  ordered_date: string;
  sample_date?: string;
  result_date?: string;
  results?: string;
  normal_range?: string;
  notes?: string;
}

// Medical Note interface
export interface MedicalNote extends BaseEntity {
  note_id?: string;
  patient: {
    id: number;
    patient_id: string;
    user: {
      full_name: string;
    };
  };
  doctor: {
    id: number;
    full_name: string;
  };
  title: string;
  content: string;
  category: 'general' | 'diagnosis' | 'treatment' | 'follow-up' | 'emergency';
  created_at: string;
  updated_at: string;
  is_private: boolean;
}

class MedicalRecordsService extends BaseApiService<MedicalRecord> {
  constructor() {
    super({
      baseURL: '/patients',
      endpoints: {
        list: '/medical-records/',
        detail: '/medical-records/:id/',
        create: '/medical-records/',
        update: '/medical-records/:id/',
        delete: '/medical-records/:id/',
      },
    });
  }

  // Get medical records for a specific patient
  async getPatientMedicalRecords(patientId: string | number) {
    return this.get<MedicalRecord[]>(`/patients/${patientId}/medical-records/`);
  }

  // Search medical records
  async searchMedicalRecords(query: string, filters?: any) {
    return this.getAll({
      search: query,
      ...filters,
    });
  }
}

class PrescriptionService extends BaseApiService<Prescription> {
  constructor() {
    super({
      baseURL: '/patients',
      endpoints: {
        list: '/prescriptions/',
        detail: '/prescriptions/:id/',
        create: '/prescriptions/',
        update: '/prescriptions/:id/',
        delete: '/prescriptions/:id/',
      },
    });
  }

  // Get prescriptions for a specific patient
  async getPatientPrescriptions(patientId: string | number) {
    return this.get<Prescription[]>(`/patients/${patientId}/prescriptions/`);
  }

  // Mark prescription as completed
  async markCompleted(prescriptionId: string | number) {
    return this.post(`/prescriptions/${prescriptionId}/mark_completed/`, {});
  }

  // Search prescriptions
  async searchPrescriptions(query: string, filters?: any) {
    return this.getAll({
      search: query,
      ...filters,
    });
  }
}

class LabTestService extends BaseApiService<LabTest> {
  constructor() {
    super({
      baseURL: '/laboratory',
      endpoints: {
        list: '/tests/',
        detail: '/tests/:id/',
        create: '/tests/',
        update: '/tests/:id/',
        delete: '/tests/:id/',
      },
    });
  }

  // Get lab tests for a specific patient
  async getPatientLabTests(patientId: string | number) {
    return this.get<LabTest[]>(`/patients/${patientId}/lab-tests/`);
  }

  // Update test results
  async updateResults(testId: string | number, results: string, normalRange?: string) {
    return this.update(testId, {
      results,
      normal_range: normalRange,
      status: 'completed',
      result_date: new Date().toISOString(),
    });
  }

  // Search lab tests
  async searchLabTests(query: string, filters?: any) {
    return this.getAll({
      search: query,
      ...filters,
    });
  }
}

class MedicalNotesService extends BaseApiService<MedicalNote> {
  constructor() {
    super({
      baseURL: '/medical-notes',
      endpoints: {
        list: '/medical-notes/',
        detail: '/medical-notes/:id/',
        create: '/medical-notes/',
        update: '/medical-notes/:id/',
        delete: '/medical-notes/:id/',
      },
    });
  }

  // Get medical notes for a specific patient
  async getPatientNotes(patientId: string | number) {
    return this.get<MedicalNote[]>(`/patients/${patientId}/medical-notes/`);
  }

  // Get medical notes by doctor
  async getDoctorNotes(doctorId?: string | number) {
    return this.getAll({
      doctor: doctorId || 'current',
    });
  }

  // Search medical notes
  async searchNotes(query: string, filters?: any) {
    return this.getAll({
      search: query,
      ...filters,
    });
  }

  // Get notes by category
  async getNotesByCategory(category: string) {
    return this.filter({ category });
  }
}

// Create and export singleton instances
export const medicalRecordsService = new MedicalRecordsService();
export const prescriptionService = new PrescriptionService();
export const labTestService = new LabTestService();
export const medicalNotesService = new MedicalNotesService();

export default {
  medicalRecords: medicalRecordsService,
  prescriptions: prescriptionService,
  labTests: labTestService,
  medicalNotes: medicalNotesService,
};
